using UnityEngine;
using Unity.Jobs;
using Unity.Collections;
using Unity.Burst;
using Unity.Mathematics;


/// <summary>
/// 高度重分布类型
/// </summary>
public enum RedistributionType
{
    None,           // 不进行重分布
    Power,          // 幂函数重分布
    Curve,          // 自定义曲线重分布
    Terraces,       // 梯田效果
    InversePower    // 反向幂函数（突出低地）
}

/// <summary>
/// 地形着色模式
/// </summary>
public enum TerrainColorMode
{
    Grayscale,      // 灰度图
    Biomes,         // 生物群系着色
    Elevation       // 高度着色
}

/// <summary>
/// 噪声地图生成器
/// 基于Red Blob Games的技术方案实现随机地图生成
/// 参考: https://www.redblobgames.com/maps/terrain-from-noise/
/// </summary>
public class NoiseMapGenerator : MonoBehaviour
{
    // 常量配置
    private const int RANDOM_OFFSET_RANGE = 100000;
    private const float MIN_SCALE = 0.0001f;
    private const float WATER_LEVEL = 0.1f;
    private const int JOB_BATCH_SIZE = 64;
    private const int CURVE_TEXTURE_HEIGHT = 128;

    // 高斯平滑核矩阵
    private static readonly float[,] GAUSSIAN_KERNEL = new float[,]
    {
        { 0.0625f, 0.125f, 0.0625f },
        { 0.125f,  0.25f,  0.125f  },
        { 0.0625f, 0.125f, 0.0625f }
    };

    [Header("数据来源")]
    public NoiseDataSO noiseData;

    [Header("预览设置")]
    public bool autoUpdate = true;
    [SerializeField] private Renderer textureRenderer;
    public Texture2D curveTexture; // 用于存储曲线图
    public bool useTerrainColoring = true;
    public TerrainColorMode colorMode = TerrainColorMode.Biomes;
    [SerializeField] private bool showStatistics = true;
    public FilterMode textureFilterMode = FilterMode.Bilinear;


    // 生成的噪声数据
    private float[,] noiseMap;


    void Start()
    {
        if (autoUpdate)
        {
            GenerateMap();
        }
    }

    /// <summary>
    /// 生成噪声地图
    /// </summary>
    public void GenerateMap()
    {
        if (!ValidateNoiseData()) return;

        // 1. 生成基础噪声
        GenerateBaseNoise();

        // 2. 应用后处理效果
        ApplyPostProcessing();

        // 3. 计算统计信息
        if (showStatistics)
        {
            CalculateMapStatistics();
        }

        // 4. 显示结果
        DisplayMap();
    }

    /// <summary>
    /// 验证噪声数据
    /// </summary>
    private bool ValidateNoiseData()
    {
        if (noiseData == null)
        {
            Debug.LogError("无法生成地图：请在 NoiseMapGenerator 组件中分配一个 NoiseDataSO 文件。");
            return false;
        }
        return true;
    }

    /// <summary>
    /// 生成基础噪声
    /// </summary>
    private void GenerateBaseNoise()
    {
        if (noiseData.useCustomAmplitudes)
        {
            noiseMap = GenerateNoiseMapWithCustomAmplitudes(
                noiseData.mapWidth, noiseData.mapHeight, noiseData.seed, noiseData.noiseScale,
                noiseData.customAmplitudes, noiseData.lacunarity, noiseData.offset, noiseData.normalizeAmplitudes);
        }
        else
        {
            noiseMap = GenerateNoiseMap(
                noiseData.mapWidth, noiseData.mapHeight, noiseData.seed, noiseData.noiseScale,
                noiseData.octaves, noiseData.persistence, noiseData.lacunarity, noiseData.offset);
        }
    }

    /// <summary>
    /// 应用后处理效果
    /// </summary>
    private void ApplyPostProcessing()
    {
        // 应用高度重分布
        if (noiseData.useRedistribution)
        {
            ApplyRedistribution(noiseMap, noiseData.redistributionType, noiseData.redistributionExponent,
                noiseData.redistributionCurve, noiseData.terraceCount, noiseData.terraceSmoothing);
        }

        // 应用地形平滑
        if (noiseData.useSmoothTerrain)
        {
            ApplySmoothTerrain(noiseMap, noiseData.smoothingIterations, noiseData.smoothingStrength);
        }
    }

    // 统一的噪声生成Job，支持标准和自定义振幅模式
    [BurstCompile(FloatPrecision.Standard, FloatMode.Fast)]
    private struct UnifiedNoiseGenerationJob : IJobParallelFor
    {
        // 输入参数
        public int mapWidth;
        public int mapHeight;
        public float scale;
        public float lacunarity;
        public Vector2 offset;
        public bool useCustomAmplitudes;
        public bool normalizeAmplitudes;
        public float persistence;
        public float amplitudeSum;

        [ReadOnly] public NativeArray<Vector2> octaveOffsets;
        [ReadOnly] public NativeArray<float> amplitudes;

        // 输出结果
        public NativeArray<float> noiseMap;

        public void Execute(int index)
        {
            int x = index % mapWidth;
            int y = index / mapWidth;

            float halfWidth = mapWidth / 2f;
            float halfHeight = mapHeight / 2f;

            float frequency = 1;
            float noiseHeight = 0;

            for (int i = 0; i < amplitudes.Length; i++)
            {
                float baseX = (x - halfWidth + offset.x) / scale;
                float baseY = (y - halfHeight + offset.y) / scale;
                float sampleX = baseX * frequency + octaveOffsets[i].x;
                float sampleY = baseY * frequency + octaveOffsets[i].y;

                float perlinValue = noise.cnoise(new float2(sampleX, sampleY));
                noiseHeight += perlinValue * amplitudes[i];

                frequency *= lacunarity;
            }

            if (useCustomAmplitudes && normalizeAmplitudes)
            {
                noiseHeight /= amplitudeSum;
            }

            noiseMap[index] = noiseHeight;
        }
    }
    /// <summary>
    /// 统一的噪声地图生成方法，支持标准和自定义振幅模式
    /// </summary>
    public static float[,] GenerateUnifiedNoiseMap(int mapWidth, int mapHeight, int seed, float scale,
        float[] amplitudes, float lacunarity, Vector2 offset, bool useCustomAmplitudes, bool normalizeAmplitudes)
    {
        float[,] noiseMap2D = new float[mapWidth, mapHeight];
        int totalPixels = mapWidth * mapHeight;

        if (amplitudes == null || amplitudes.Length == 0)
        {
            Debug.LogWarning("振幅数组为空，返回空地图");
            return noiseMap2D;
        }

        int octaves = amplitudes.Length;

        // 准备Job所需的数据
        var noiseMap = new NativeArray<float>(totalPixels, Allocator.TempJob);
        var octaveOffsets = new NativeArray<Vector2>(octaves, Allocator.TempJob);
        var nativeAmplitudes = new NativeArray<float>(amplitudes, Allocator.TempJob);

        // 生成随机偏移
        GenerateRandomOffsets(octaveOffsets, octaves, seed);

        if (scale <= 0) scale = MIN_SCALE;

        // 计算振幅总和（用于标准化）
        float amplitudeSum = 0f;
        if (useCustomAmplitudes && normalizeAmplitudes)
        {
            for (int i = 0; i < amplitudes.Length; i++)
            {
                amplitudeSum += amplitudes[i];
            }
        }
        else
        {
            amplitudeSum = 1f; // 避免除以零
        }

        // 创建并调度统一Job
        var noiseJob = new UnifiedNoiseGenerationJob
        {
            mapWidth = mapWidth,
            mapHeight = mapHeight,
            scale = scale,
            lacunarity = lacunarity,
            offset = offset,
            useCustomAmplitudes = useCustomAmplitudes,
            normalizeAmplitudes = normalizeAmplitudes,
            amplitudeSum = amplitudeSum,
            octaveOffsets = octaveOffsets,
            amplitudes = nativeAmplitudes,
            noiseMap = noiseMap,
        };

        JobHandle jobHandle = noiseJob.Schedule(totalPixels, JOB_BATCH_SIZE);
        jobHandle.Complete();

        // 标准化处理
        NormalizeNoiseMap(noiseMap, noiseMap2D, mapWidth, mapHeight);

        // 清理NativeArrays
        noiseMap.Dispose();
        octaveOffsets.Dispose();
        nativeAmplitudes.Dispose();

        return noiseMap2D;
    }

    /// <summary>
    /// 标准化噪声地图数据
    /// </summary>
    private static void NormalizeNoiseMap(NativeArray<float> noiseMap, float[,] noiseMap2D, int mapWidth, int mapHeight)
    {
        int totalPixels = mapWidth * mapHeight;

        // 查找min/max值
        var (minNoise, maxNoise) = FindMinMaxValues(noiseMap, totalPixels);

        // 标准化并复制回2D数组
        CopyAndNormalize1DTo2D(noiseMap, noiseMap2D, mapWidth, mapHeight, minNoise, maxNoise);
    }

    /// <summary>
    /// 查找数组中的最小值和最大值
    /// </summary>
    private static (float min, float max) FindMinMaxValues(NativeArray<float> array, int length)
    {
        float minValue = float.MaxValue;
        float maxValue = float.MinValue;

        for (int i = 0; i < length; i++)
        {
            float val = array[i];
            if (val > maxValue) maxValue = val;
            if (val < minValue) minValue = val;
        }

        return (minValue, maxValue);
    }

    /// <summary>
    /// 将1D数组标准化并复制到2D数组
    /// </summary>
    private static void CopyAndNormalize1DTo2D(NativeArray<float> source, float[,] destination,
        int width, int height, float minValue, float maxValue)
    {
        int totalPixels = width * height;
        for (int i = 0; i < totalPixels; i++)
        {
            int x = i % width;
            int y = i / width;
            destination[x, y] = Mathf.InverseLerp(minValue, maxValue, source[i]);
        }
    }

    /// <summary>
    /// 生成随机偏移数组
    /// </summary>
    private static void GenerateRandomOffsets(NativeArray<Vector2> octaveOffsets, int octaves, int seed)
    {
        var prng = new System.Random(seed);
        for (int i = 0; i < octaves; i++)
        {
            float offsetX = prng.Next(-RANDOM_OFFSET_RANGE, RANDOM_OFFSET_RANGE);
            float offsetY = prng.Next(-RANDOM_OFFSET_RANGE, RANDOM_OFFSET_RANGE);
            octaveOffsets[i] = new Vector2(offsetX, offsetY);
        }
    }

    /// <summary>
    /// 生成标准噪声地图（向后兼容）
    /// </summary>
    public static float[,] GenerateNoiseMap(int mapWidth, int mapHeight, int seed, float scale, int octaves, float persistence, float lacunarity, Vector2 offset)
    {
        // 生成标准振幅数组
        float[] amplitudes = new float[octaves];
        float amplitude = 1f;
        for (int i = 0; i < octaves; i++)
        {
            amplitudes[i] = amplitude;
            amplitude *= persistence;
        }

        return GenerateUnifiedNoiseMap(mapWidth, mapHeight, seed, scale, amplitudes, lacunarity, offset, false, false);
    }



    /// <summary>
    /// 使用自定义振幅生成噪声地图（向后兼容）
    /// </summary>
    public static float[,] GenerateNoiseMapWithCustomAmplitudes(int mapWidth, int mapHeight, int seed, float scale, float[] amplitudes, float lacunarity, Vector2 offset, bool normalize)
    {
        return GenerateUnifiedNoiseMap(mapWidth, mapHeight, seed, scale, amplitudes, lacunarity, offset, true, normalize);
    }

    /// <summary>
    /// 应用高度重分布
    /// </summary>
    private void ApplyRedistribution(float[,] noiseMap, RedistributionType type, float exponent, AnimationCurve curve, float terraceCount, float terraceSmoothing)
    {
        int width = noiseMap.GetLength(0);
        int height = noiseMap.GetLength(1);

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float originalValue = noiseMap[x, y];
                float redistributedValue = originalValue;

                switch (type)
                {
                    case RedistributionType.None:
                        redistributedValue = originalValue;
                        break;

                    case RedistributionType.Power:
                        redistributedValue = Mathf.Pow(originalValue, exponent);
                        break;

                    case RedistributionType.InversePower:
                        redistributedValue = 1f - Mathf.Pow(1f - originalValue, exponent);
                        break;

                    case RedistributionType.Curve:
                        redistributedValue = curve.Evaluate(originalValue);
                        break;

                    case RedistributionType.Terraces:
                        redistributedValue = ApplyTerraceEffect(originalValue, terraceCount, terraceSmoothing);
                        break;
                }

                noiseMap[x, y] = Mathf.Clamp01(redistributedValue);
            }
        }
    }

    /// <summary>
    /// 应用梯田效果
    /// </summary>
    private float ApplyTerraceEffect(float value, float terraceCount, float smoothing)
    {
        if (terraceCount <= 1) return value;

        float terraceValue = value * terraceCount;
        float terraceFloor = Mathf.Floor(terraceValue);
        float terraceFraction = terraceValue - terraceFloor;

        // 应用平滑
        if (smoothing > 0)
        {
            terraceFraction = Mathf.SmoothStep(0, 1, terraceFraction / smoothing);
            terraceFraction = Mathf.Clamp01(terraceFraction);
        }
        else
        {
            terraceFraction = 0;
        }

        return (terraceFloor + terraceFraction) / terraceCount;
    }

    /// <summary>
    /// 应用圆滑化处理
    /// </summary>
    private void ApplySmoothTerrain(float[,] noiseMap, int iterations, float strength)
    {
        if (iterations <= 0 || strength <= 0) return;

        int width = noiseMap.GetLength(0);
        int height = noiseMap.GetLength(1);

        float[,] tempMap = new float[width, height];

        for (int iter = 0; iter < iterations; iter++)
        {
            System.Array.Copy(noiseMap, tempMap, width * height);

            for (int y = 1; y < height - 1; y++)
            {
                for (int x = 1; x < width - 1; x++)
                {
                    float smoothedValue = ApplyGaussianSmooth(tempMap, x, y, width, height);
                    noiseMap[x, y] = Mathf.Lerp(tempMap[x, y], smoothedValue, strength);
                }
            }
        }
    }

    /// <summary>
    /// 应用高斯平滑滤波
    /// </summary>
    private float ApplyGaussianSmooth(float[,] heightMap, int centerX, int centerY, int width, int height)
    {
        float result = 0f;

        for (int dy = -1; dy <= 1; dy++)
        {
            for (int dx = -1; dx <= 1; dx++)
            {
                int sampleX = centerX + dx;
                int sampleY = centerY + dy;

                sampleX = Mathf.Clamp(sampleX, 0, width - 1);
                sampleY = Mathf.Clamp(sampleY, 0, height - 1);

                float weight = GAUSSIAN_KERNEL[dy + 1, dx + 1];
                result += heightMap[sampleX, sampleY] * weight;
            }
        }

        return result;
    }

    /// <summary>
    /// 显示地图
    /// </summary>
    private void DisplayMap()
    {
        // 1. 生成并应用场景中的地形纹理
        UpdateSceneTexture();

        // 2. 生成用于Inspector预览的曲线图
        curveTexture = GenerateCurveTexture(noiseMap);
    }

    /// <summary>
    /// 更新场景纹理
    /// </summary>
    private void UpdateSceneTexture()
    {
        if (textureRenderer == null) return;

        Texture2D texture = useTerrainColoring
            ? TextureFromHeightMapWithTerrain(noiseMap, colorMode, textureFilterMode)
            : TextureFromHeightMap(noiseMap, textureFilterMode);

        textureRenderer.sharedMaterial.mainTexture = texture;
    }

    /// <summary>
    /// 计算地图统计信息
    /// </summary>
    private void CalculateMapStatistics()
    {
        if (noiseMap == null) return;

        int width = noiseMap.GetLength(0);
        int height = noiseMap.GetLength(1);
        int totalPixels = width * height;

        float minHeight = float.MaxValue;
        float maxHeight = float.MinValue;
        float totalHeight = 0f;
        int waterPixels = 0;

        const float waterLevel = WATER_LEVEL;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float heightValue = noiseMap[x, y];

                if (heightValue < minHeight) minHeight = heightValue;
                if (heightValue > maxHeight) maxHeight = heightValue;

                totalHeight += heightValue;

                if (heightValue < waterLevel) waterPixels++;
            }
        }

        if (showStatistics)
        {
            float averageHeight = totalHeight / totalPixels;
            float waterPercentage = (float)waterPixels / totalPixels * 100f;
            float landPercentage = (float)(totalPixels - waterPixels) / totalPixels * 100f;

            Debug.Log($"地图统计信息:\n" +
                     $"高度范围: {minHeight:F3} - {maxHeight:F3}\n" +
                     $"平均高度: {averageHeight:F3}\n" +
                     $"水域占比: {waterPercentage:F1}%\n" +
                     $"陆地占比: {landPercentage:F1}%\n" +
                     $"总像素数: {totalPixels}");
        }
    }

    /// <summary>
    /// 从高度图创建纹理
    /// </summary>
    public static Texture2D TextureFromHeightMap(float[,] heightMap, FilterMode filterMode = FilterMode.Point)
    {
        int width = heightMap.GetLength(0);
        int height = heightMap.GetLength(1);

        Color[] colorMap = new Color[width * height];
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                colorMap[y * width + x] = Color.Lerp(Color.black, Color.white, heightMap[x, y]);
            }
        }

        return TextureFromColorMap(colorMap, width, height, filterMode);
    }

    /// <summary>
    /// 从高度图创建带地形着色的纹理
    /// </summary>
    public Texture2D TextureFromHeightMapWithTerrain(float[,] heightMap, TerrainColorMode colorMode, FilterMode filterMode = FilterMode.Point)
    {
        int width = heightMap.GetLength(0);
        int height = heightMap.GetLength(1);

        Color[] colorMap = new Color[width * height];
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float heightValue = heightMap[x, y];
                Color terrainColor = GetTerrainColor(heightValue, colorMode);
                colorMap[y * width + x] = terrainColor;
            }
        }

        return TextureFromColorMap(colorMap, width, height, filterMode);
    }

    /// <summary>
    /// 根据高度值获取地形颜色
    /// </summary>
    public Color GetTerrainColor(float height, TerrainColorMode colorMode)
    {
        switch (colorMode)
        {
            case TerrainColorMode.Grayscale:
                return Color.Lerp(Color.black, Color.white, height);

            case TerrainColorMode.Biomes:
                return noiseData.GetBiomeColor(height); // 从SO获取颜色

            case TerrainColorMode.Elevation:
                // 这个也可以考虑移到SO中，但暂时保留
                if (height < 0.5f)
                {
                    return Color.Lerp(Color.blue, Color.green, height * 2f);
                }
                else
                {
                    return Color.Lerp(Color.green, Color.red, (height - 0.5f) * 2f);
                }

            default:
                return Color.white;
        }
    }

    /// <summary>
    /// 从颜色数组创建纹理
    /// </summary>
    public static Texture2D TextureFromColorMap(Color[] colorMap, int width, int height, FilterMode filterMode = FilterMode.Point)
    {
        Texture2D texture = new Texture2D(width, height);
        texture.filterMode = filterMode;
        texture.wrapMode = TextureWrapMode.Clamp;
        texture.SetPixels(colorMap);
        texture.Apply();
        return texture;
    }


    /// <summary>
    /// 获取生成的噪声地图数据
    /// </summary>
    public float[,] GetNoiseMap()
    {
        return noiseMap;
    }


    /// <summary>
    /// 从噪声数据生成地形剖面曲线纹理
    /// </summary>
    public Texture2D GenerateCurveTexture(float[,] noiseMap)
    {
        if (noiseMap == null) return new Texture2D(1, 1);

        int width = noiseMap.GetLength(0);
        int height = noiseMap.GetLength(1);
        int curveHeight = CURVE_TEXTURE_HEIGHT;
        Texture2D texture = new Texture2D(width, curveHeight);

        Color backgroundColor = new Color(0.1f, 0.1f, 0.1f, 1f);
        Color lineColor = Color.green;

        // 1. 填充背景
        Color[] backgroundPixels = new Color[width * curveHeight];
        for (int i = 0; i < backgroundPixels.Length; i++)
        {
            backgroundPixels[i] = backgroundColor;
        }
        texture.SetPixels(backgroundPixels);

        // 2. 提取中间一行的噪声数据作为剖面
        int midY = height / 2;
        float[] profile = new float[width];
        for (int x = 0; x < width; x++)
        {
            profile[x] = noiseMap[x, midY];
        }

        // 3. 绘制曲线
        for (int x = 0; x < width; x++)
        {
            int y = (int)(profile[x] * (curveHeight - 1));
            texture.SetPixel(x, y, lineColor);

            // 可以画一条粗一点的线
            if (y > 0) texture.SetPixel(x, y - 1, lineColor);
            if (y < curveHeight - 2) texture.SetPixel(x, y + 1, lineColor);
        }

        texture.Apply();
        return texture;
    }
}
