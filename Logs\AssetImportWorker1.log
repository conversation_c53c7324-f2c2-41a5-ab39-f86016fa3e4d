Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.33f1c1 (ea5182f68133) revision 15356290'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 16280 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
F:/The Lightless Crown
-logFile
Logs/AssetImportWorker1.log
-srvPort
60736
Successfully changed project path to: F:/The Lightless Crown
F:/The Lightless Crown
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [7552] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 819089711 [EditorId] 819089711 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NEVNEH0) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [7552] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 819089711 [EditorId] 819089711 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NEVNEH0) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
Refreshing native plugins compatible for Editor in 108.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.33f1c1 (ea5182f68133)
[Subsystems] Discovering subsystems at path D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/The Lightless Crown/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 960 (ID=0x1401)
    Vendor:   NVIDIA
    VRAM:     4040 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56852
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.023869 seconds.
- Loaded All Assemblies, in  0.766 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.378 seconds
Domain Reload Profiling: 1136ms
	BeginReloadAssembly (339ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (130ms)
	LoadAllAssembliesAndSetupDomain (219ms)
		LoadAssemblies (281ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (214ms)
			TypeCache.Refresh (213ms)
				TypeCache.ScanAssembly (189ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (378ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (299ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (3ms)
			ProcessInitializeOnLoadAttributes (202ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.083 seconds
Refreshing native plugins compatible for Editor in 17.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.846 seconds
Domain Reload Profiling: 3913ms
	BeginReloadAssembly (300ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (1604ms)
		LoadAssemblies (894ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (872ms)
			TypeCache.Refresh (816ms)
				TypeCache.ScanAssembly (757ms)
			ScanForSourceGeneratedMonoScriptInfo (40ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1846ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1632ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (749ms)
			ProcessInitializeOnLoadMethodAttributes (714ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.20 seconds
Refreshing native plugins compatible for Editor in 17.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5652 Unused Serialized files (Serialized files now loaded: 0)
Unloading 81 unused Assets / (351.5 KB). Loaded Objects now: 6122.
Memory consumption went from 220.3 MB to 220.0 MB.
Total: 7.725200 ms (FindLiveObjects: 0.592800 ms CreateObjectMapping: 0.694200 ms MarkObjects: 6.141000 ms  DeleteObjects: 0.295600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/NoiseMapEditor.cs: 8cc59c437366f0524cc05eea97fddec7 -> 
  custom:scripting/monoscript/fileName/Quad.cs: 26ab027030fa49bfb06395df9e8c5377 -> 
  custom:scripting/monoscript/fileName/Noise.cs: 8c769ab96d8e4f66380db355c1907102 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/MapDisplay.cs: 4bf08403a1cf1ea495af2630314b25b4 -> 
  custom:scripting/monoscript/fileName/NoiseGenerator.cs: 610cc9956887c8462c301bcdea0dfd48 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MapGeneratorEditor.cs: 7f316cd05ce3e42181b8d84580cc521e -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/NoiseData.cs: 3a30bf2d4b60ff82d8e0b788740c4abd -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/NoiseMapSettings.cs: 84e799b17285abd7e990e98b3777cb67 -> 
  custom:scripting/monoscript/fileName/NoiseMapConfigSO.cs: 3e22af1aba73cf5aac7897c5341a64e3 -> 
  custom:scripting/monoscript/fileName/NoiseMapSettingsCreator.cs: 48446b5151bbcb9190db2b63fc37bf4d -> 
  custom:scripting/monoscript/fileName/NoiseDebugger.cs: 2d02c8793acd39fddfbc10cccbde60f2 -> 
  custom:scripting/monoscript/fileName/NoiseMap.cs: 6575f69319da5e3a1d047a421b5d9838 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/NoiseDataSO.cs: e85e1b41027e74711b160aa5f2669baf -> c9f89c44112993b061c7976858ef437b
  custom:scripting/monoscript/fileName/NoiseAlgorithm.cs: 00f66ba4f38c9d6a60587158045d0fbb -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/MapGenerator.cs: 29ed077e59fdd73d3d4890fc3a314e9a -> 
  custom:scripting/monoscript/fileName/MapGeneratorTest.cs: 6b2e3a3a163206297a860818f5fcc804 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/QuickMapTest.cs: 0161e3b37b720d40784271fcf38e6f3d -> 
  custom:scripting/monoscript/fileName/NoiseConfigSO.cs: 72a7679233f3b9f415c8b9b9613733e2 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/MapData.cs: 0b914e04b0d876f1e214855d87ff9853 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TerrainType.cs: 2bb2f500403f934dac7d9c14dc35d812 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.076 seconds
Refreshing native plugins compatible for Editor in 18.56 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.636 seconds
Domain Reload Profiling: 4695ms
	BeginReloadAssembly (271ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (74ms)
	LoadAllAssembliesAndSetupDomain (645ms)
		LoadAssemblies (712ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (72ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			ScanForSourceGeneratedMonoScriptInfo (35ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (3636ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1582ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (812ms)
			ProcessInitializeOnLoadMethodAttributes (598ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 16.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.6 KB). Loaded Objects now: 6126.
Memory consumption went from 191.2 MB to 190.9 MB.
Total: 5.099100 ms (FindLiveObjects: 0.508100 ms CreateObjectMapping: 0.284300 ms MarkObjects: 4.167200 ms  DeleteObjects: 0.138100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/NoiseMapEditor.cs: 8cc59c437366f0524cc05eea97fddec7 -> 
  custom:scripting/monoscript/fileName/Quad.cs: 26ab027030fa49bfb06395df9e8c5377 -> 
  custom:scripting/monoscript/fileName/Noise.cs: 8c769ab96d8e4f66380db355c1907102 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/MapDisplay.cs: 4bf08403a1cf1ea495af2630314b25b4 -> 
  custom:scripting/monoscript/fileName/NoiseGenerator.cs: 610cc9956887c8462c301bcdea0dfd48 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MapGeneratorEditor.cs: 7f316cd05ce3e42181b8d84580cc521e -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/NoiseData.cs: 3a30bf2d4b60ff82d8e0b788740c4abd -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/NoiseMapSettings.cs: 84e799b17285abd7e990e98b3777cb67 -> 
  custom:scripting/monoscript/fileName/NoiseMapConfigSO.cs: 3e22af1aba73cf5aac7897c5341a64e3 -> 
  custom:scripting/monoscript/fileName/NoiseMapSettingsCreator.cs: 48446b5151bbcb9190db2b63fc37bf4d -> 
  custom:scripting/monoscript/fileName/NoiseDebugger.cs: 2d02c8793acd39fddfbc10cccbde60f2 -> 
  custom:scripting/monoscript/fileName/NoiseMap.cs: 6575f69319da5e3a1d047a421b5d9838 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/NoiseDataSO.cs: e85e1b41027e74711b160aa5f2669baf -> c9f89c44112993b061c7976858ef437b
  custom:scripting/monoscript/fileName/NoiseAlgorithm.cs: 00f66ba4f38c9d6a60587158045d0fbb -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/MapGenerator.cs: 29ed077e59fdd73d3d4890fc3a314e9a -> 
  custom:scripting/monoscript/fileName/MapGeneratorTest.cs: 6b2e3a3a163206297a860818f5fcc804 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/QuickMapTest.cs: 0161e3b37b720d40784271fcf38e6f3d -> 
  custom:scripting/monoscript/fileName/NoiseConfigSO.cs: 72a7679233f3b9f415c8b9b9613733e2 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/MapData.cs: 0b914e04b0d876f1e214855d87ff9853 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TerrainType.cs: 2bb2f500403f934dac7d9c14dc35d812 -> 
========================================================================
Received Import Request.
  Time since last request: 315864.336255 seconds.
  path: Assets/Scripts/Map/NoiseDataSO.cs
  artifactKey: Guid(7f18a83cf0baf7340aae0b462483e817) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/NoiseDataSO.cs using Guid(7f18a83cf0baf7340aae0b462483e817) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'c021f135dcda4bb17946678fa0bd7bf7') in 0.070422 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 54.531239 seconds.
  path: Assets/Scripts/Map/Noise
  artifactKey: Guid(c42617ba707f2e648999adcf0e5fe1b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Noise using Guid(c42617ba707f2e648999adcf0e5fe1b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'c8d498f5a6d8bc3fb95ba6b1a2da292f') in 0.005262 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.466127 seconds.
  path: Assets/Scripts/Map/NoiseMapGenerator.cs
  artifactKey: Guid(79a440da11004624da3a97292ac9a096) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/NoiseMapGenerator.cs using Guid(79a440da11004624da3a97292ac9a096) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '83ddc65cc3dc8fc932b568d9e14cf896') in 0.002131 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.443 seconds
Refreshing native plugins compatible for Editor in 16.21 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.440 seconds
Domain Reload Profiling: 8870ms
	BeginReloadAssembly (713ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (316ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (1543ms)
		LoadAssemblies (1709ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (96ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (5ms)
			ScanForSourceGeneratedMonoScriptInfo (48ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (6441ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1615ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (166ms)
			ProcessInitializeOnLoadAttributes (833ms)
			ProcessInitializeOnLoadMethodAttributes (575ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 28.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.6 KB). Loaded Objects now: 6130.
Memory consumption went from 191.2 MB to 190.9 MB.
Total: 117.251500 ms (FindLiveObjects: 0.736900 ms CreateObjectMapping: 2.643200 ms MarkObjects: 113.623000 ms  DeleteObjects: 0.246500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/NoiseMapEditor.cs: 8cc59c437366f0524cc05eea97fddec7 -> 
  custom:scripting/monoscript/fileName/Quad.cs: 26ab027030fa49bfb06395df9e8c5377 -> 
  custom:scripting/monoscript/fileName/Noise.cs: 8c769ab96d8e4f66380db355c1907102 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/MapDisplay.cs: 4bf08403a1cf1ea495af2630314b25b4 -> 
  custom:scripting/monoscript/fileName/NoiseGenerator.cs: 610cc9956887c8462c301bcdea0dfd48 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MapGeneratorEditor.cs: 7f316cd05ce3e42181b8d84580cc521e -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/NoiseData.cs: 3a30bf2d4b60ff82d8e0b788740c4abd -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/NoiseMapSettings.cs: 84e799b17285abd7e990e98b3777cb67 -> 
  custom:scripting/monoscript/fileName/NoiseMapConfigSO.cs: 3e22af1aba73cf5aac7897c5341a64e3 -> 
  custom:scripting/monoscript/fileName/NoiseMapSettingsCreator.cs: 48446b5151bbcb9190db2b63fc37bf4d -> 
  custom:scripting/monoscript/fileName/NoiseDebugger.cs: 2d02c8793acd39fddfbc10cccbde60f2 -> 
  custom:scripting/monoscript/fileName/NoiseMap.cs: 6575f69319da5e3a1d047a421b5d9838 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/NoiseDataSO.cs: d36a9290f9f07af433a98e5864703d8b -> e85e1b41027e74711b160aa5f2669baf
  custom:scripting/monoscript/fileName/NoiseAlgorithm.cs: 00f66ba4f38c9d6a60587158045d0fbb -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/MapGenerator.cs: 29ed077e59fdd73d3d4890fc3a314e9a -> 
  custom:scripting/monoscript/fileName/MapGeneratorTest.cs: 6b2e3a3a163206297a860818f5fcc804 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/QuickMapTest.cs: 0161e3b37b720d40784271fcf38e6f3d -> 
  custom:scripting/monoscript/fileName/NoiseConfigSO.cs: 72a7679233f3b9f415c8b9b9613733e2 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/MapData.cs: 0b914e04b0d876f1e214855d87ff9853 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TerrainType.cs: 2bb2f500403f934dac7d9c14dc35d812 -> 
